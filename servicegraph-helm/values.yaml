global:
  namespace: "default"

beyla:
  enabled: true

  image:
    repository: grafana/beyla
    tag: "1.7.3"
    pullPolicy: IfNotPresent

  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

  discovery:
    # -- Kubernetes namespace to monitor
    namespace: "." # current namesapce

otelCollector:
  enabled: true

  replicas: 1

  image:
    repository: otel/opentelemetry-collector-contrib
    tag: "0.91.0"
    pullPolicy: IfNotPresent

  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

  export:
    endpoint: ""
    secretName: ""
    headers: {}