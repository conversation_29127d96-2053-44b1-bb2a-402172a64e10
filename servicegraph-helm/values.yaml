# Global configuration
global:
  # -- Namespace to deploy components in
  namespace: "default"

# Beyla configuration for distributed tracing
beyla:
  # -- Enable Beyla deployment
  enabled: true

  # -- Number of replicas for Beyla deployment
  replicas: 1

  image:
    # -- Beyla image repository
    repository: grafana/beyla
    # -- Beyla image tag
    tag: "1.7.3"
    # -- Image pull policy
    pullPolicy: IfNotPresent

  # -- Resource limits and requests for Beyla
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

  # -- Service discovery configuration
  discovery:
    # -- Kubernetes namespace to monitor (use "." for current namespace)
    namespace: "."

# OpenTelemetry Collector configuration
otelCollector:
  # -- Enable OpenTelemetry Collector deployment
  enabled: true

  # -- Number of replicas for OTel Collector deployment
  replicas: 1

  image:
    # -- OTel Collector image repository
    repository: otel/opentelemetry-collector-contrib
    # -- OTel Collector image tag
    tag: "0.91.0"
    # -- Image pull policy
    pullPolicy: IfNotPresent

  # -- Resource limits and requests for OTel Collector
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

  # -- Export configuration for traces
  exporters:
    # -- OTLP exporter configuration
    otlp:
      # -- Enable OTLP exporter
      enabled: false
      # -- OTLP endpoint (e.g., http://jaeger:4317)
      endpoint: ""
      # -- Additional headers for OTLP exporter
      headers: {}

    # -- Jaeger exporter configuration
    jaeger:
      # -- Enable Jaeger exporter
      enabled: false
      # -- Jaeger endpoint (e.g., http://jaeger:14250)
      endpoint: ""

    # -- Zipkin exporter configuration
    zipkin:
      # -- Enable Zipkin exporter
      enabled: false
      # -- Zipkin endpoint (e.g., http://zipkin:9411/api/v2/spans)
      endpoint: ""

    # -- Logging exporter (for debugging)
    logging:
      # -- Enable logging exporter
      enabled: true
      # -- Log level for traces
      loglevel: info