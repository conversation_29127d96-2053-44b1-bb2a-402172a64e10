{{- if .Values.otelCollector.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "servicegraph.fullname" . }}-otel-collector
  namespace: {{ include "servicegraph.namespace" . }}
  labels:
    {{- include "servicegraph.otelCollector.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.otelCollector.replicas }}
  selector:
    matchLabels:
      {{- include "servicegraph.otelCollector.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "servicegraph.otelCollector.selectorLabels" . | nindent 8 }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/otel-collector-configmap.yaml") . | sha256sum }}
    spec:
      containers:
      - name: otel-collector
        image: {{ .Values.otelCollector.image.repository }}:{{ .Values.otelCollector.image.tag }}
        imagePullPolicy: {{ .Values.otelCollector.image.pullPolicy }}
        args:
        - --config=/etc/otel-collector-config/otel-collector-config.yml
        ports:
        - name: otlp-grpc
          containerPort: 4317
          protocol: TCP
        - name: otlp-http
          containerPort: 4318
          protocol: TCP
        volumeMounts:
        - name: otel-collector-config
          mountPath: /etc/otel-collector-config
          readOnly: true
        resources:
          {{- toYaml .Values.otelCollector.resources | nindent 10 }}
        livenessProbe:
          httpGet:
            path: /
            port: 13133
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 13133
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: otel-collector-config
        configMap:
          name: {{ include "servicegraph.fullname" . }}-otel-collector-config
{{- end }}
