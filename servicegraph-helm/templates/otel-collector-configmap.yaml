{{- if .Values.otelCollector.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "servicegraph.fullname" . }}-otel-collector-config
  namespace: {{ include "servicegraph.namespace" . }}
  labels:
    {{- include "servicegraph.otelCollector.labels" . | nindent 4 }}
data:
  otel-collector-config.yml: |
    receivers:
      # OTLP receiver for traces from Beyla
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
    
    processors:
      # Batch processor for better performance
      batch:
        timeout: 1s
        send_batch_size: 1024
        send_batch_max_size: 2048
      
      # Memory limiter to prevent OOM
      memory_limiter:
        limit_mib: 400
        spike_limit_mib: 100
        check_interval: 5s
    
    exporters:
      {{- if .Values.otelCollector.exporters.logging.enabled }}
      # Logging exporter for debugging
      logging:
        loglevel: {{ .Values.otelCollector.exporters.logging.loglevel }}
      {{- end }}
      
      {{- if .Values.otelCollector.exporters.otlp.enabled }}
      # OTLP exporter
      otlp:
        endpoint: {{ .Values.otelCollector.exporters.otlp.endpoint | quote }}
        {{- if .Values.otelCollector.exporters.otlp.headers }}
        headers:
          {{- range $key, $value := .Values.otelCollector.exporters.otlp.headers }}
          {{ $key }}: {{ $value | quote }}
          {{- end }}
        {{- end }}
      {{- end }}
      
      {{- if .Values.otelCollector.exporters.jaeger.enabled }}
      # Jaeger exporter
      jaeger:
        endpoint: {{ .Values.otelCollector.exporters.jaeger.endpoint | quote }}
        tls:
          insecure: true
      {{- end }}
      
      {{- if .Values.otelCollector.exporters.zipkin.enabled }}
      # Zipkin exporter
      zipkin:
        endpoint: {{ .Values.otelCollector.exporters.zipkin.endpoint | quote }}
      {{- end }}
    
    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [memory_limiter, batch]
          exporters: [{{- $exporters := list }}{{- if .Values.otelCollector.exporters.logging.enabled }}{{- $exporters = append $exporters "logging" }}{{- end }}{{- if .Values.otelCollector.exporters.otlp.enabled }}{{- $exporters = append $exporters "otlp" }}{{- end }}{{- if .Values.otelCollector.exporters.jaeger.enabled }}{{- $exporters = append $exporters "jaeger" }}{{- end }}{{- if .Values.otelCollector.exporters.zipkin.enabled }}{{- $exporters = append $exporters "zipkin" }}{{- end }}{{ join ", " $exporters }}]
      
      extensions: []
      
      telemetry:
        logs:
          level: info
{{- end }}
