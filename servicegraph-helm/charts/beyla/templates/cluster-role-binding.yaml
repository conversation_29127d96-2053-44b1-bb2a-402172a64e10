{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "beyla.fullname" . }}
  labels:
    {{- include "beyla.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
subjects:
  - kind: ServiceAccount
    name: {{ include "beyla.serviceAccountName" . }}
    namespace: {{ include "beyla.namespace" .}}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "beyla.fullname" . }}
{{- end }}
