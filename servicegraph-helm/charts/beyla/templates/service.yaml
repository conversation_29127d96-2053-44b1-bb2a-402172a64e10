{{- if .Values.service.enabled }}
{{- $root := . }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "beyla.fullname" . }}
  namespace: {{ include "beyla.namespace" .}}
  labels:
    {{- include "beyla.labels" . | nindent 4 }}
    app.kubernetes.io/component: networking
    {{- with .Values.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- tpl (toYaml . | nindent 4) $root }}
  {{- end }}
spec:
  {{- if (or (eq .Values.service.type "ClusterIP") (empty .Values.service.type)) }}
  type: ClusterIP
  {{- with .Values.service.clusterIP }}
  clusterIP: {{ . }}
  {{- end }}
  {{- else if eq .Values.service.type "LoadBalancer" }}
  type: LoadBalancer
  {{- with .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ . }}
  {{- end }}
  {{- with .Values.service.loadBalancerClass }}
  loadBalancerClass: {{ . }}
  {{- end }}
  {{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- else }}
  type: {{ .Values.service.type }}
  {{- end }}
  {{- with .Values.service.externalIPs }}
  externalIPs:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.service.externalTrafficPolicy }}
  externalTrafficPolicy: {{ . }}
  {{- end }}
  ports:
    {{- if or (.Values.service.targetPort) (.Values.config.data.prometheus_export) }}
    - name: {{ .Values.service.portName }}
      port: {{ .Values.service.port }}
      protocol: TCP
      targetPort: {{ .Values.service.targetPort | default .Values.config.data.prometheus_export.port }}
      {{- with .Values.service.appProtocol }}
      appProtocol: {{ . }}
      {{- end }}
      {{- if (and (eq .Values.service.type "NodePort") (not (empty .Values.service.nodePort))) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
    {{- end }}
    {{- if (and (or (.Values.service.internalMetrics.targetPort) (.Values.config.data.internal_metrics)) (not (eq .Values.config.data.prometheus_export.port .Values.config.data.internal_metrics.prometheus.port))) }}
    - name: {{ .Values.service.internalMetrics.portName }}
      port: {{ .Values.service.internalMetrics.port }}
      protocol: TCP
      targetPort: {{ .Values.service.internalMetrics.targetPort | default .Values.config.data.internal_metrics.prometheus.port }}
      {{- with .Values.service.internalMetrics.appProtocol }}
      appProtocol: {{ . }}
      {{- end }}
      {{- if (and (eq .Values.service.type "NodePort") (not (empty .Values.service.internalMetrics.nodePort))) }}
      nodePort: {{ .Values.service.internalMetrics.nodePort }}
      {{- end }}
    {{- end }}
  selector:
    {{- include "beyla.selectorLabels" . | nindent 4 }}
{{- end }}
