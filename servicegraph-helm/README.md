# ServiceGraph Helm Chart

A Helm chart for distributed tracing with <PERSON><PERSON> and OpenTelemetry Collector.

## Overview

This chart deploys:
- **<PERSON><PERSON>** as a Deployment (not DaemonSet) configured for distributed tracing only
- **OpenTelemetry Collector** as a Deployment to process and export traces from Beyla

## Features

- ✅ Beyla configured for distributed tracing only (no metrics)
- ✅ OpenTelemetry Collector with configurable exporters
- ✅ Minimal configuration surface in values.yaml
- ✅ Support for multiple trace exporters (OTLP, Jaeger, Zipkin, Logging)
- ✅ Kubernetes service discovery and filtering
- ✅ RBAC configuration for Beyla

## Installation

```bash
# Install with default values (logging exporter only)
helm install my-servicegraph ./servicegraph-helm

# Install with custom namespace
helm install my-servicegraph ./servicegraph-helm \
  --set global.namespace=observability

# Install with J<PERSON>ger exporter
helm install my-servicegraph ./servicegraph-helm \
  --set otelCollector.exporters.jaeger.enabled=true \
  --set otelCollector.exporters.jaeger.endpoint=http://jaeger:14250 \
  --set otelCollector.exporters.logging.enabled=false
```

## Configuration

### Key Values

| Parameter | Description | Default |
|-----------|-------------|---------|
| `global.namespace` | Namespace to deploy components | `default` |
| `beyla.enabled` | Enable Beyla deployment | `true` |
| `beyla.replicas` | Number of Beyla replicas | `1` |
| `beyla.discovery.namespace` | K8s namespace to monitor | `"."` |
| `otelCollector.enabled` | Enable OTel Collector | `true` |
| `otelCollector.exporters.otlp.enabled` | Enable OTLP exporter | `false` |
| `otelCollector.exporters.otlp.endpoint` | OTLP endpoint | `""` |
| `otelCollector.exporters.jaeger.enabled` | Enable Jaeger exporter | `false` |
| `otelCollector.exporters.jaeger.endpoint` | Jaeger endpoint | `""` |
| `otelCollector.exporters.zipkin.enabled` | Enable Zipkin exporter | `false` |
| `otelCollector.exporters.zipkin.endpoint` | Zipkin endpoint | `""` |
| `otelCollector.exporters.logging.enabled` | Enable logging exporter | `true` |

### Example: Export to Jaeger

```yaml
# values.yaml
global:
  namespace: observability

otelCollector:
  exporters:
    logging:
      enabled: false
    jaeger:
      enabled: true
      endpoint: http://jaeger-collector:14250
```

### Example: Export to OTLP endpoint

```yaml
# values.yaml
otelCollector:
  exporters:
    logging:
      enabled: false
    otlp:
      enabled: true
      endpoint: http://tempo:4317
      headers:
        authorization: "Bearer your-token"
```

## Architecture

```
┌─────────────────┐    OTLP/HTTP    ┌─────────────────┐    Configured    ┌─────────────────┐
│                 │    Port 4318    │                 │    Exporters     │                 │
│  Grafana Beyla  ├────────────────►│ OTel Collector  ├─────────────────►│ Trace Backends  │
│   (Deployment)  │                 │   (Deployment)  │                  │ (Jaeger/Tempo)  │
└─────────────────┘                 └─────────────────┘                  └─────────────────┘
```

## Requirements

- Kubernetes 1.19+
- Helm 3.0+
- Cluster with eBPF support for Beyla

## Uninstall

```bash
helm uninstall my-servicegraph
```
