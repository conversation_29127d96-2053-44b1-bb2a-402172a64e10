.PHONY: build clean docker-build docker-push deploy test

# Build all services
build:
	@echo "Building all services..."
	go build -o bin/gateway ./cmd/gateway
	go build -o bin/users ./cmd/users
	go build -o bin/orders ./cmd/orders
	go build -o bin/inventory ./cmd/inventory
	go build -o bin/payments ./cmd/payments
	go build -o bin/database ./cmd/database
	go build -o bin/notifications ./cmd/notifications
	go build -o bin/audit ./cmd/audit

# Clean build artifacts
clean:
	rm -rf bin/

# Build Docker images
docker-build:
	docker build -t microservices-demo/gateway -f docker/Dockerfile.gateway .
	docker build -t microservices-demo/users -f docker/Dockerfile.users .
	docker build -t microservices-demo/orders -f docker/Dockerfile.orders .
	docker build -t microservices-demo/inventory -f docker/Dockerfile.inventory .
	docker build -t microservices-demo/payments -f docker/Dockerfile.payments .
	docker build -t microservices-demo/database -f docker/Dockerfile.database .
	docker build -t microservices-demo/notifications -f docker/Dockerfile.notifications .
	docker build -t microservices-demo/audit -f docker/Dockerfile.audit .

# Deploy to Kubernetes
deploy:
	kubectl apply -f k8s/

# Remove from Kubernetes
undeploy:
	kubectl delete -f k8s/

# Run tests
test:
	@echo "Running integration tests..."
	@sleep 5  # Wait for services to start
	curl -f http://localhost:8080/health || exit 1
	@echo "Creating test user..."
	curl -X POST http://localhost:8080/users -H "Content-Type: application/json" -d '{"name":"Test User","email":"<EMAIL>"}'
	@echo "Creating test order..."
	curl -X POST http://localhost:8080/orders -H "Content-Type: application/json" -d '{"user_id":"1","product_id":"test-product","quantity":2}'
	@echo "Tests completed successfully!"

# Run locally for development
run-local:
	@echo "Starting all services locally..."
	./bin/database &
	sleep 2
	./bin/users &
	./bin/inventory &
	./bin/payments &
	./bin/notifications &
	./bin/audit &
	sleep 2
	./bin/orders &
	sleep 2
	./bin/gateway &
	@echo "All services started. Gateway available at http://localhost:8080"

# Stop local services
stop-local:
	pkill -f "./bin/"
