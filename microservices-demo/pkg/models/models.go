package models

import "time"

// User represents a user in the system
type User struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// CreateUserRequest represents a request to create a user
type CreateUserRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

// Order represents an order in the system
type Order struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	ProductID string    `json:"product_id"`
	Quantity  int       `json:"quantity"`
	Status    string    `json:"status"`
	Total     float64   `json:"total"`
	CreatedAt time.Time `json:"created_at"`
}

// CreateOrderRequest represents a request to create an order
type CreateOrderRequest struct {
	UserID    string `json:"user_id"`
	ProductID string `json:"product_id"`
	Quantity  int    `json:"quantity"`
}

// Product represents a product in inventory
type Product struct {
	ID       string  `json:"id"`
	Name     string  `json:"name"`
	Price    float64 `json:"price"`
	Quantity int     `json:"quantity"`
}

// InventoryCheckRequest represents a request to check inventory
type InventoryCheckRequest struct {
	ProductID string `json:"product_id"`
	Quantity  int    `json:"quantity"`
}

// InventoryCheckResponse represents a response from inventory check
type InventoryCheckResponse struct {
	Available bool    `json:"available"`
	Price     float64 `json:"price"`
}

// Payment represents a payment transaction
type Payment struct {
	ID      string  `json:"id"`
	OrderID string  `json:"order_id"`
	Amount  float64 `json:"amount"`
	Status  string  `json:"status"`
}

// ProcessPaymentRequest represents a request to process payment
type ProcessPaymentRequest struct {
	OrderID string  `json:"order_id"`
	Amount  float64 `json:"amount"`
}

// ProcessPaymentResponse represents a response from payment processing
type ProcessPaymentResponse struct {
	PaymentID string `json:"payment_id"`
	Status    string `json:"status"`
}

// Notification represents a notification
type Notification struct {
	ID      string `json:"id"`
	UserID  string `json:"user_id"`
	Type    string `json:"type"`
	Message string `json:"message"`
	Sent    bool   `json:"sent"`
}

// SendNotificationRequest represents a request to send notification
type SendNotificationRequest struct {
	UserID  string `json:"user_id"`
	Type    string `json:"type"`
	Message string `json:"message"`
}

// AuditLog represents an audit log entry
type AuditLog struct {
	ID        string    `json:"id"`
	Service   string    `json:"service"`
	Action    string    `json:"action"`
	UserID    string    `json:"user_id,omitempty"`
	Details   string    `json:"details"`
	Timestamp time.Time `json:"timestamp"`
}

// CreateAuditLogRequest represents a request to create an audit log
type CreateAuditLogRequest struct {
	Service string `json:"service"`
	Action  string `json:"action"`
	UserID  string `json:"user_id,omitempty"`
	Details string `json:"details"`
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}
