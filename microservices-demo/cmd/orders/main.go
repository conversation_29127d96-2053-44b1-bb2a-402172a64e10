package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	httpClient "microservices-demo/pkg/http"
	"microservices-demo/pkg/models"
)

type OrdersService struct {
	orders         map[string]*models.Order
	ordersMutex    sync.RWMutex
	nextID         int
	usersClient    *httpClient.Client
	inventoryClient *httpClient.Client
	paymentsClient *httpClient.Client
	notificationsClient *httpClient.Client
	auditClient    *httpClient.Client
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Get service URLs from environment or use defaults
	usersURL := getEnv("USERS_SERVICE_URL", "http://localhost:8081")
	inventoryURL := getEnv("INVENTORY_SERVICE_URL", "http://localhost:8083")
	paymentsURL := getEnv("PAYMENTS_SERVICE_URL", "http://localhost:8084")
	notificationsURL := getEnv("NOTIFICATIONS_SERVICE_URL", "http://localhost:8086")
	auditURL := getEnv("AUDIT_SERVICE_URL", "http://localhost:8087")

	service := &OrdersService{
		orders:              make(map[string]*models.Order),
		nextID:              1,
		usersClient:         httpClient.NewClient(usersURL),
		inventoryClient:     httpClient.NewClient(inventoryURL),
		paymentsClient:      httpClient.NewClient(paymentsURL),
		notificationsClient: httpClient.NewClient(notificationsURL),
		auditClient:         httpClient.NewClient(auditURL),
	}

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", service.healthHandler).Methods("GET")

	// Order routes
	r.HandleFunc("/orders", service.createOrderHandler).Methods("POST")
	r.HandleFunc("/orders/{id}", service.getOrderHandler).Methods("GET")
	r.HandleFunc("/orders", service.listOrdersHandler).Methods("GET")

	// Add middleware
	r.Use(loggingMiddleware)

	port := getEnv("PORT", "8082")
	log.Info().Str("port", port).Msg("Orders service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (s *OrdersService) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "orders",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *OrdersService) createOrderHandler(w http.ResponseWriter, r *http.Request) {
	var req models.CreateOrderRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	// Verify user exists
	var user models.User
	if err := s.usersClient.Get(ctx, "/users/"+req.UserID, &user); err != nil {
		log.Error().Err(err).Str("user_id", req.UserID).Msg("User not found")
		http.Error(w, "User not found", http.StatusBadRequest)
		return
	}

	// Check inventory
	inventoryReq := models.InventoryCheckRequest{
		ProductID: req.ProductID,
		Quantity:  req.Quantity,
	}
	var inventoryResp models.InventoryCheckResponse
	if err := s.inventoryClient.Post(ctx, "/inventory/check", inventoryReq, &inventoryResp); err != nil {
		log.Error().Err(err).Msg("Failed to check inventory")
		http.Error(w, "Failed to check inventory", http.StatusInternalServerError)
		return
	}

	if !inventoryResp.Available {
		http.Error(w, "Product not available", http.StatusBadRequest)
		return
	}

	// Create order
	s.ordersMutex.Lock()
	orderID := strconv.Itoa(s.nextID)
	s.nextID++
	
	total := inventoryResp.Price * float64(req.Quantity)
	order := &models.Order{
		ID:        orderID,
		UserID:    req.UserID,
		ProductID: req.ProductID,
		Quantity:  req.Quantity,
		Status:    "pending",
		Total:     total,
		CreatedAt: time.Now(),
	}
	s.orders[orderID] = order
	s.ordersMutex.Unlock()

	// Process payment
	paymentReq := models.ProcessPaymentRequest{
		OrderID: orderID,
		Amount:  total,
	}
	var paymentResp models.ProcessPaymentResponse
	if err := s.paymentsClient.Post(ctx, "/payments", paymentReq, &paymentResp); err != nil {
		log.Error().Err(err).Msg("Payment processing failed")
		order.Status = "payment_failed"
	} else if paymentResp.Status == "success" {
		order.Status = "confirmed"
	} else {
		order.Status = "payment_failed"
	}

	// Send notification
	notificationReq := models.SendNotificationRequest{
		UserID:  req.UserID,
		Type:    "order_confirmation",
		Message: fmt.Sprintf("Your order %s has been %s", orderID, order.Status),
	}
	if err := s.notificationsClient.Post(ctx, "/notifications", notificationReq, nil); err != nil {
		log.Error().Err(err).Msg("Failed to send notification")
		// Continue anyway
	}

	// Create audit log
	auditReq := models.CreateAuditLogRequest{
		Service: "orders",
		Action:  "order_created",
		UserID:  req.UserID,
		Details: fmt.Sprintf("Order %s created with status %s", orderID, order.Status),
	}
	if err := s.auditClient.Post(ctx, "/audit", auditReq, nil); err != nil {
		log.Error().Err(err).Msg("Failed to create audit log")
		// Continue anyway
	}

	log.Info().Str("order_id", orderID).Str("status", order.Status).Msg("Order created")

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(order)
}

func (s *OrdersService) getOrderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	orderID := vars["id"]

	s.ordersMutex.RLock()
	order, exists := s.orders[orderID]
	s.ordersMutex.RUnlock()

	if !exists {
		http.Error(w, "Order not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(order)
}

func (s *OrdersService) listOrdersHandler(w http.ResponseWriter, r *http.Request) {
	s.ordersMutex.RLock()
	orders := make([]*models.Order, 0, len(s.orders))
	for _, order := range s.orders {
		orders = append(orders, order)
	}
	s.ordersMutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(orders)
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "orders").
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "orders").
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
