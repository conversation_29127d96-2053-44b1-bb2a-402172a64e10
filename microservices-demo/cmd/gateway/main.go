package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	httpClient "microservices-demo/pkg/http"
	"microservices-demo/pkg/models"
)

type Gateway struct {
	usersClient   *httpClient.Client
	ordersClient  *httpClient.Client
	auditClient   *httpClient.Client
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Get service URLs from environment or use defaults
	usersURL := getEnv("USERS_SERVICE_URL", "http://localhost:8081")
	ordersURL := getEnv("ORDERS_SERVICE_URL", "http://localhost:8082")
	auditURL := getEnv("AUDIT_SERVICE_URL", "http://localhost:8087")

	gateway := &Gateway{
		usersClient:  httpClient.NewClient(usersURL),
		ordersClient: httpClient.NewClient(ordersURL),
		auditClient:  httpClient.NewClient(auditURL),
	}

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", gateway.healthHandler).Methods("GET")

	// User routes
	r.HandleFunc("/users", gateway.createUserHandler).Methods("POST")
	r.HandleFunc("/users/{id}", gateway.getUserHandler).Methods("GET")

	// Order routes
	r.HandleFunc("/orders", gateway.createOrderHandler).Methods("POST")
	r.HandleFunc("/orders/{id}", gateway.getOrderHandler).Methods("GET")

	// Add middleware for logging and tracing
	r.Use(loggingMiddleware)
	r.Use(tracingMiddleware)

	port := getEnv("PORT", "8080")
	log.Info().Str("port", port).Msg("Gateway service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (g *Gateway) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "gateway",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (g *Gateway) createUserHandler(w http.ResponseWriter, r *http.Request) {
	var req models.CreateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()
	
	// Create audit log
	auditReq := models.CreateAuditLogRequest{
		Service: "gateway",
		Action:  "create_user_request",
		Details: fmt.Sprintf("Creating user: %s", req.Email),
	}
	g.auditClient.Post(ctx, "/audit", auditReq, nil)

	// Forward to users service
	var user models.User
	if err := g.usersClient.Post(ctx, "/users", req, &user); err != nil {
		log.Error().Err(err).Msg("Failed to create user")
		http.Error(w, "Failed to create user", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(user)
}

func (g *Gateway) getUserHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["id"]

	ctx := r.Context()

	var user models.User
	if err := g.usersClient.Get(ctx, "/users/"+userID, &user); err != nil {
		log.Error().Err(err).Str("user_id", userID).Msg("Failed to get user")
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(user)
}

func (g *Gateway) createOrderHandler(w http.ResponseWriter, r *http.Request) {
	var req models.CreateOrderRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	// Create audit log
	auditReq := models.CreateAuditLogRequest{
		Service: "gateway",
		Action:  "create_order_request",
		UserID:  req.UserID,
		Details: fmt.Sprintf("Creating order for product: %s", req.ProductID),
	}
	g.auditClient.Post(ctx, "/audit", auditReq, nil)

	// Forward to orders service
	var order models.Order
	if err := g.ordersClient.Post(ctx, "/orders", req, &order); err != nil {
		log.Error().Err(err).Msg("Failed to create order")
		http.Error(w, "Failed to create order", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(order)
}

func (g *Gateway) getOrderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	orderID := vars["id"]

	ctx := r.Context()

	var order models.Order
	if err := g.ordersClient.Get(ctx, "/orders/"+orderID, &order); err != nil {
		log.Error().Err(err).Str("order_id", orderID).Msg("Failed to get order")
		http.Error(w, "Order not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(order)
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("remote_addr", r.RemoteAddr).
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func tracingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Simple trace ID generation for demonstration
		traceID := fmt.Sprintf("trace-%d", time.Now().UnixNano())
		ctx := context.WithValue(r.Context(), "trace-id", traceID)
		
		w.Header().Set("X-Trace-ID", traceID)
		
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
