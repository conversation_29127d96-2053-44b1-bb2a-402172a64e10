package main

import (
	"encoding/json"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	httpClient "microservices-demo/pkg/http"
	"microservices-demo/pkg/models"
)

type InventoryService struct {
	products      map[string]*models.Product
	productsMutex sync.RWMutex
	dbClient      *httpClient.Client
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Get service URLs from environment or use defaults
	dbURL := getEnv("DATABASE_SERVICE_URL", "http://localhost:8085")

	service := &InventoryService{
		products: make(map[string]*models.Product),
		dbClient: httpClient.NewClient(dbURL),
	}

	// Initialize some sample products
	service.initializeProducts()

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", service.healthHandler).Methods("GET")

	// Inventory routes
	r.HandleFunc("/inventory/check", service.checkInventoryHandler).Methods("POST")
	r.HandleFunc("/inventory/{id}", service.getProductHandler).Methods("GET")
	r.HandleFunc("/inventory", service.listProductsHandler).Methods("GET")

	// Add middleware
	r.Use(loggingMiddleware)

	port := getEnv("PORT", "8083")
	log.Info().Str("port", port).Msg("Inventory service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (s *InventoryService) initializeProducts() {
	products := []*models.Product{
		{ID: "laptop-001", Name: "Gaming Laptop", Price: 1299.99, Quantity: 10},
		{ID: "mouse-001", Name: "Wireless Mouse", Price: 29.99, Quantity: 50},
		{ID: "keyboard-001", Name: "Mechanical Keyboard", Price: 89.99, Quantity: 25},
		{ID: "monitor-001", Name: "4K Monitor", Price: 399.99, Quantity: 15},
		{ID: "headset-001", Name: "Gaming Headset", Price: 79.99, Quantity: 30},
		{ID: "test-product", Name: "Test Product", Price: 19.99, Quantity: 100},
	}

	s.productsMutex.Lock()
	for _, product := range products {
		s.products[product.ID] = product
	}
	s.productsMutex.Unlock()

	log.Info().Int("count", len(products)).Msg("Initialized products")
}

func (s *InventoryService) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "inventory",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *InventoryService) checkInventoryHandler(w http.ResponseWriter, r *http.Request) {
	var req models.InventoryCheckRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "inventory",
		"operation": "select",
		"id": req.ProductID,
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.productsMutex.RLock()
	product, exists := s.products[req.ProductID]
	s.productsMutex.RUnlock()

	response := models.InventoryCheckResponse{
		Available: false,
		Price:     0,
	}

	if exists && product.Quantity >= req.Quantity {
		response.Available = true
		response.Price = product.Price

		// Simulate reducing inventory
		s.productsMutex.Lock()
		product.Quantity -= req.Quantity
		s.productsMutex.Unlock()

		log.Info().
			Str("product_id", req.ProductID).
			Int("quantity_requested", req.Quantity).
			Int("quantity_remaining", product.Quantity).
			Msg("Inventory check successful")
	} else {
		log.Warn().
			Str("product_id", req.ProductID).
			Int("quantity_requested", req.Quantity).
			Bool("product_exists", exists).
			Msg("Inventory check failed")
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *InventoryService) getProductHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	productID := vars["id"]

	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "inventory",
		"operation": "select",
		"id": productID,
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.productsMutex.RLock()
	product, exists := s.products[productID]
	s.productsMutex.RUnlock()

	if !exists {
		http.Error(w, "Product not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(product)
}

func (s *InventoryService) listProductsHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "inventory",
		"operation": "select_all",
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.productsMutex.RLock()
	products := make([]*models.Product, 0, len(s.products))
	for _, product := range s.products {
		products = append(products, product)
	}
	s.productsMutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(products)
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "inventory").
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "inventory").
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
