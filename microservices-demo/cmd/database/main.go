package main

import (
	"encoding/json"
	"math/rand"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"microservices-demo/pkg/models"
)

type DatabaseService struct {
	// Simulated database - in reality this would be a real database connection
}

type QueryRequest struct {
	Table     string      `json:"table"`
	Operation string      `json:"operation"`
	ID        string      `json:"id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}

type QueryResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	service := &DatabaseService{}

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", service.healthHandler).Methods("GET")

	// Database routes
	r.HandleFunc("/query", service.queryHandler).Methods("POST")
	r.HandleFunc("/status", service.statusHandler).Methods("GET")

	// Add middleware
	r.Use(loggingMiddleware)

	port := getEnv("PORT", "8085")
	log.Info().Str("port", port).Msg("Database service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (s *DatabaseService) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "database",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *DatabaseService) queryHandler(w http.ResponseWriter, r *http.Request) {
	var req QueryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Simulate database operation delay
	delay := time.Duration(rand.Intn(100)+50) * time.Millisecond
	time.Sleep(delay)

	log.Info().
		Str("table", req.Table).
		Str("operation", req.Operation).
		Str("id", req.ID).
		Dur("delay", delay).
		Msg("Database query executed")

	// Simulate occasional database errors (5% failure rate)
	if rand.Float32() < 0.05 {
		response := QueryResponse{
			Success: false,
			Error:   "Database connection timeout",
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	// Simulate successful response
	response := QueryResponse{
		Success: true,
		Data:    generateMockData(req.Table, req.Operation),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *DatabaseService) statusHandler(w http.ResponseWriter, r *http.Request) {
	status := map[string]interface{}{
		"status":           "healthy",
		"connections":      rand.Intn(50) + 10,
		"active_queries":   rand.Intn(20),
		"cache_hit_ratio":  0.85 + rand.Float64()*0.1,
		"uptime_seconds":   time.Now().Unix() - 1000000, // Fake uptime
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

func generateMockData(table, operation string) interface{} {
	switch operation {
	case "select":
		return map[string]interface{}{
			"id":         "mock-id",
			"table":      table,
			"found":      true,
			"timestamp":  time.Now(),
		}
	case "select_all":
		return map[string]interface{}{
			"table":      table,
			"count":      rand.Intn(100) + 1,
			"timestamp":  time.Now(),
		}
	case "insert":
		return map[string]interface{}{
			"table":      table,
			"inserted_id": "new-id-" + table,
			"timestamp":  time.Now(),
		}
	case "update":
		return map[string]interface{}{
			"table":        table,
			"rows_affected": 1,
			"timestamp":    time.Now(),
		}
	case "delete":
		return map[string]interface{}{
			"table":        table,
			"rows_affected": 1,
			"timestamp":    time.Now(),
		}
	default:
		return map[string]interface{}{
			"table":     table,
			"operation": operation,
			"timestamp": time.Now(),
		}
	}
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "database").
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "database").
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
