package main

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	httpClient "microservices-demo/pkg/http"
	"microservices-demo/pkg/models"
)

type PaymentsService struct {
	payments      map[string]*models.Payment
	paymentsMutex sync.RWMutex
	nextID        int
	auditClient   *httpClient.Client
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Get service URLs from environment or use defaults
	auditURL := getEnv("AUDIT_SERVICE_URL", "http://localhost:8087")

	service := &PaymentsService{
		payments:    make(map[string]*models.Payment),
		nextID:      1,
		auditClient: httpClient.NewClient(auditURL),
	}

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", service.healthHandler).Methods("GET")

	// Payment routes
	r.HandleFunc("/payments", service.processPaymentHandler).Methods("POST")
	r.HandleFunc("/payments/{id}", service.getPaymentHandler).Methods("GET")
	r.HandleFunc("/payments", service.listPaymentsHandler).Methods("GET")

	// Add middleware
	r.Use(loggingMiddleware)

	port := getEnv("PORT", "8084")
	log.Info().Str("port", port).Msg("Payments service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (s *PaymentsService) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "payments",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *PaymentsService) processPaymentHandler(w http.ResponseWriter, r *http.Request) {
	var req models.ProcessPaymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	// Simulate payment processing delay
	time.Sleep(time.Duration(rand.Intn(500)+100) * time.Millisecond)

	s.paymentsMutex.Lock()
	paymentID := strconv.Itoa(s.nextID)
	s.nextID++

	// Simulate payment success/failure (90% success rate)
	status := "success"
	if rand.Float32() < 0.1 {
		status = "failed"
	}

	payment := &models.Payment{
		ID:      paymentID,
		OrderID: req.OrderID,
		Amount:  req.Amount,
		Status:  status,
	}
	s.payments[paymentID] = payment
	s.paymentsMutex.Unlock()

	// Create audit log
	auditReq := models.CreateAuditLogRequest{
		Service: "payments",
		Action:  "payment_processed",
		Details: fmt.Sprintf("Payment %s for order %s: %s (%.2f)", paymentID, req.OrderID, status, req.Amount),
	}
	if err := s.auditClient.Post(ctx, "/audit", auditReq, nil); err != nil {
		log.Error().Err(err).Msg("Failed to create audit log")
		// Continue anyway
	}

	log.Info().
		Str("payment_id", paymentID).
		Str("order_id", req.OrderID).
		Str("status", status).
		Float64("amount", req.Amount).
		Msg("Payment processed")

	response := models.ProcessPaymentResponse{
		PaymentID: paymentID,
		Status:    status,
	}

	w.Header().Set("Content-Type", "application/json")
	if status == "success" {
		w.WriteHeader(http.StatusCreated)
	} else {
		w.WriteHeader(http.StatusPaymentRequired)
	}
	json.NewEncoder(w).Encode(response)
}

func (s *PaymentsService) getPaymentHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	paymentID := vars["id"]

	s.paymentsMutex.RLock()
	payment, exists := s.payments[paymentID]
	s.paymentsMutex.RUnlock()

	if !exists {
		http.Error(w, "Payment not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(payment)
}

func (s *PaymentsService) listPaymentsHandler(w http.ResponseWriter, r *http.Request) {
	s.paymentsMutex.RLock()
	payments := make([]*models.Payment, 0, len(s.payments))
	for _, payment := range s.payments {
		payments = append(payments, payment)
	}
	s.paymentsMutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(payments)
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "payments").
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "payments").
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
