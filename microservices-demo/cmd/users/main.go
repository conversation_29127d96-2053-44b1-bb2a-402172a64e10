package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	httpClient "microservices-demo/pkg/http"
	"microservices-demo/pkg/models"
)

type UsersService struct {
	users      map[string]*models.User
	usersMutex sync.RWMutex
	nextID     int
	dbClient   *httpClient.Client
	auditClient *httpClient.Client
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	// Get service URLs from environment or use defaults
	dbURL := getEnv("DATABASE_SERVICE_URL", "http://localhost:8085")
	auditURL := getEnv("AUDIT_SERVICE_URL", "http://localhost:8087")

	service := &UsersService{
		users:       make(map[string]*models.User),
		nextID:      1,
		dbClient:    httpClient.NewClient(dbURL),
		auditClient: httpClient.NewClient(auditURL),
	}

	r := mux.NewRouter()

	// Health check
	r.HandleFunc("/health", service.healthHandler).Methods("GET")

	// User routes
	r.HandleFunc("/users", service.createUserHandler).Methods("POST")
	r.HandleFunc("/users/{id}", service.getUserHandler).Methods("GET")
	r.HandleFunc("/users", service.listUsersHandler).Methods("GET")

	// Add middleware
	r.Use(loggingMiddleware)

	port := getEnv("PORT", "8081")
	log.Info().Str("port", port).Msg("Users service starting")

	server := &http.Server{
		Addr:         ":" + port,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
	}

	log.Fatal().Err(server.ListenAndServe()).Msg("Server failed")
}

func (s *UsersService) healthHandler(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:  "healthy",
		Service: "users",
		Version: "1.0.0",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *UsersService) createUserHandler(w http.ResponseWriter, r *http.Request) {
	var req models.CreateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "users",
		"operation": "insert",
		"data": req,
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.usersMutex.Lock()
	userID := strconv.Itoa(s.nextID)
	s.nextID++
	
	user := &models.User{
		ID:    userID,
		Name:  req.Name,
		Email: req.Email,
	}
	s.users[userID] = user
	s.usersMutex.Unlock()

	// Create audit log
	auditReq := models.CreateAuditLogRequest{
		Service: "users",
		Action:  "user_created",
		UserID:  userID,
		Details: fmt.Sprintf("User created: %s (%s)", req.Name, req.Email),
	}
	if err := s.auditClient.Post(ctx, "/audit", auditReq, nil); err != nil {
		log.Error().Err(err).Msg("Failed to create audit log")
		// Continue anyway
	}

	log.Info().Str("user_id", userID).Str("email", req.Email).Msg("User created")

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(user)
}

func (s *UsersService) getUserHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	userID := vars["id"]

	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "users",
		"operation": "select",
		"id": userID,
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.usersMutex.RLock()
	user, exists := s.users[userID]
	s.usersMutex.RUnlock()

	if !exists {
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(user)
}

func (s *UsersService) listUsersHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Simulate database call
	dbReq := map[string]interface{}{
		"table": "users",
		"operation": "select_all",
	}
	if err := s.dbClient.Post(ctx, "/query", dbReq, nil); err != nil {
		log.Error().Err(err).Msg("Database operation failed")
		// Continue anyway for demo purposes
	}

	s.usersMutex.RLock()
	users := make([]*models.User, 0, len(s.users))
	for _, user := range s.users {
		users = append(users, user)
	}
	s.usersMutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(users)
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "users").
			Msg("Request started")

		next.ServeHTTP(w, r)

		log.Info().
			Str("method", r.Method).
			Str("path", r.URL.Path).
			Str("service", "users").
			Dur("duration", time.Since(start)).
			Msg("Request completed")
	})
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
