# Microservices Demo Application

A collection of Go microservices designed to test distributed tracing with Beyla and OpenTelemetry.

## Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Gateway   │───▶│   Users     │───▶│  Database   │
│   Service   │    │   Service   │    │   Service   │
└─────────────┘    └─────────────┘    └─────────────┘
       │                  │
       ▼                  ▼
┌─────────────┐    ┌─────────────┐
│   Orders    │───▶│  Inventory  │
│   Service   │    │   Service   │
└─────────────┘    └─────────────┘
       │                  │
       ▼                  ▼
┌─────────────┐    ┌─────────────┐
│  Payments   │    │ Notifications│
│   Service   │    │   Service   │
└─────────────┘    └─────────────┘
       │
       ▼
┌─────────────┐
│   Audit     │
│   Service   │
└─────────────┘
```

## Services

1. **Gateway Service** (Port 8080) - API Gateway that routes requests
2. **Users Service** (Port 8081) - User management
3. **Orders Service** (Port 8082) - Order processing
4. **Inventory Service** (Port 8083) - Product inventory
5. **Payments Service** (Port 8084) - Payment processing
6. **Database Service** (Port 8085) - Simulated database
7. **Notifications Service** (Port 8086) - Email/SMS notifications
8. **Audit Service** (Port 8087) - Audit logging

## Deployment

```bash
# Build all services
make build

# Deploy to Kubernetes
kubectl apply -f k8s/

# Or use Skaffold for development
skaffold dev
```

## Testing

```bash
# Create a user
curl -X POST http://localhost:8080/users -d '{"name":"<PERSON>","email":"<EMAIL>"}'

# Create an order (triggers multiple service calls)
curl -X POST http://localhost:8080/orders -d '{"user_id":"1","product_id":"123","quantity":2}'
```

This will generate traces across all services for testing your servicegraph setup.
